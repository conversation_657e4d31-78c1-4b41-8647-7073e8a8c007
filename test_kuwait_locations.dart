// ملف اختبار لتجربة وظائف المحافظات والمدن الكويتية
import 'package:flutter/material.dart';
import 'lib/data/kuwait_locations.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار المحافظات الكويتية',
      home: TestPage(),
    );
  }
}

class TestPage extends StatefulWidget {
  @override
  _TestPageState createState() => _TestPageState();
}

class _TestPageState extends State<TestPage> {
  String _selectedGovernorate = "محافظة العاصمة";
  String? _selectedCity;
  List<String> _availableCities = [];

  @override
  void initState() {
    super.initState();
    _updateAvailableCities();
  }

  void _updateAvailableCities() {
    setState(() {
      _availableCities = KuwaitLocations.getAreasByGovernorate(_selectedGovernorate);
      if (_availableCities.isNotEmpty) {
        _selectedCity = _availableCities.first;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('اختبار المحافظات الكويتية'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            // المحافظة
            DropdownButtonFormField<String>(
              value: _selectedGovernorate,
              decoration: InputDecoration(
                labelText: "المحافظة",
                border: OutlineInputBorder(),
              ),
              items: KuwaitLocations.governorates.map((gov) {
                return DropdownMenuItem<String>(
                  value: gov,
                  child: Text(gov),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedGovernorate = value;
                    _updateAvailableCities();
                  });
                }
              },
            ),
            SizedBox(height: 16),
            
            // المدينة
            DropdownButtonFormField<String>(
              value: _selectedCity,
              decoration: InputDecoration(
                labelText: "المدينة",
                border: OutlineInputBorder(),
              ),
              items: _availableCities.map((city) {
                return DropdownMenuItem<String>(
                  value: city,
                  child: Text(city),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedCity = value;
                  });
                }
              },
            ),
            SizedBox(height: 16),
            
            // عرض النتائج
            Text(
              'المحافظة المختارة: $_selectedGovernorate',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Text(
              'المدينة المختارة: $_selectedCity',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Text(
              'عدد المدن المتاحة: ${_availableCities.length}',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
