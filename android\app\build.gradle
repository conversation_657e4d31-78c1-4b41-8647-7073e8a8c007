plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

// تحميل إعدادات التوقيع الرقمي
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.codnet.krea"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId = "com.codnet.krea"
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = 1
        versionName = "1.0.0"

        // إعدادات إضافية للإصدار
        multiDexEnabled = true
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    // إعداد التوقيع الرقمي
    signingConfigs {
        release {
            if (keystorePropertiesFile.exists()) {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
                storePassword keystoreProperties['storePassword']
            }
        }
    }

    buildTypes {
        debug {
            // تم إزالة applicationIdSuffix مؤقتاً لتجنب مشاكل Firebase
            debuggable = true
            minifyEnabled = false
        }

        release {
            minifyEnabled = false
            shrinkResources = false
            debuggable = false

            // استخدام التوقيع الرقمي إذا كان متوفراً، وإلا استخدم مفاتيح التطوير
            signingConfig = keystorePropertiesFile.exists() ? signingConfigs.release : signingConfigs.debug

            // إعدادات الأمان
            buildConfigField "boolean", "DEBUG_MODE", "false"
        }
    }

    // إعدادات إضافية للأداء
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }

    // دعم اللغات
    bundle {
        language {
            enableSplit = false
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // تم إزالة Play Core libraries لتجنب مشاكل التوافق مع SDK 34
}
